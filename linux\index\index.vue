<template>
	<page-container :isShowNav="false" class="relative linux-index-container">
		<!-- 背景图片容器 -->
		<view class="bg-image-container">
			<image
				:src="backgroundImage"
				mode="aspectFill"
				class="bg-image"
				:style="backgroundImageStyle"
			/>
			<!-- 渐变遮罩层，用于更好的内容可读性 -->
			<view class="bg-overlay"></view>
		</view>

		<!-- 自定义导航栏 -->
		<custom-nav
			bg-color="transparent"
			title=""
			:is-back="true"
			class="nav-transparent"
		>
		</custom-nav>

		<!-- 页面内容区域 -->
		<view class="content-wrapper">
			<!-- 这里放置你的页面内容 -->
			<view class="demo-content">
				<text class="demo-text">页面内容区域</text>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref, computed } from 'vue';
	import CustomNav from '@/components/customNav/index.vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import { getTheme, THEME_TYPE } from '@/hooks/useTheme.js';

	// 获取当前主题
	const currentTheme = ref(getTheme());

	// 根据主题动态选择背景图片
	const backgroundImage = computed(() => {
		return currentTheme.value === THEME_TYPE.DARK
			? require('@/static/index/bg-dark.png')
			: require('@/static/index/bg-light.png');
	});

	// 背景图片样式配置
	const backgroundImageStyle = computed(() => {
		return {
			// 确保图片完全覆盖容器
			width: '100%',
			height: '100%',
			// 添加过渡动画，主题切换时更平滑
			transition: 'opacity 0.3s ease'
		};
	});

	// 监听主题变化
	uni.$on('themeChange', (theme) => {
		currentTheme.value = theme;
	});
</script>

<style lang="scss" scoped>
.linux-index-container {
	overflow: hidden;
}

/* 背景图片容器 */
.bg-image-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 60vh; /* 使用视口高度，更好的响应式效果 */
	min-height: 400rpx; /* 最小高度保证 */
	max-height: 600rpx; /* 最大高度限制 */
	z-index: 0;
	overflow: hidden;
}

/* 背景图片样式 */
.bg-image {
	width: 100%;
	height: 100%;
	object-fit: cover; /* 保持图片比例，避免变形 */
	object-position: center top; /* 图片定位到顶部中心 */
}

/* 渐变遮罩层 */
.bg-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(
		180deg,
		rgba(0, 0, 0, 0) 0%,
		rgba(0, 0, 0, 0.1) 70%,
		rgba(0, 0, 0, 0.2) 100%
	);
	z-index: 1;
}

/* 透明导航栏样式 */
.nav-transparent {
	position: relative;
	z-index: 10;
	background: transparent !important;
}

/* 内容包装器 */
.content-wrapper {
	position: relative;
	z-index: 5;
	margin-top: 300rpx; /* 为背景图片留出空间 */
	padding: 40rpx 30rpx;
	background: var(--bg-color);
	border-radius: 30rpx 30rpx 0 0; /* 圆角设计，更现代 */
	min-height: calc(100vh - 300rpx);
}

/* 演示内容样式 */
.demo-content {
	padding: 40rpx 0;
	text-align: center;
}

.demo-text {
	font-size: 32rpx;
	color: var(--text-color-primary);
	font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.bg-image-container {
		height: 50vh;
		min-height: 350rpx;
	}

	.content-wrapper {
		margin-top: 250rpx;
		min-height: calc(100vh - 250rpx);
	}
}

/* 大屏设备适配 */
@media screen and (min-width: 1200rpx) {
	.bg-image-container {
		height: 65vh;
		max-height: 800rpx;
	}

	.content-wrapper {
		margin-top: 350rpx;
		min-height: calc(100vh - 350rpx);
	}
}

/* 主题适配 */
.theme-dark {
	.bg-overlay {
		background: linear-gradient(
			180deg,
			rgba(0, 0, 0, 0) 0%,
			rgba(0, 0, 0, 0.2) 70%,
			rgba(0, 0, 0, 0.4) 100%
		);
	}

	.content-wrapper {
		background: var(--bg-color);
		color: var(--text-color-primary);
	}
}
</style>
